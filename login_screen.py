"""
شاشة دخول متقدمة وجميلة جداً - تصميم عصري وأنيق
"""

import sys
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QApplication, QMessageBox, QCheckBox, QFrame,
                            QGraphicsDropShadowEffect, QWidget)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QRect
from PyQt5.QtGui import QFont, QPixmap, QPainter, QBrush, QLinearGradient, QColor, QPen
from database import get_session, User

class LoginScreen(QDialog):
    """شاشة دخول متقدمة وجميلة جداً"""

    login_successful = pyqtSignal(object, object)

    def __init__(self):
        super().__init__()
        self.session = None
        self.user = None
        self.setup_ui()
        self.setup_animations()

    def setup_ui(self):
        """إعداد واجهة متقدمة وجميلة"""
        self.setWindowTitle("Smart Finish - Advanced Login")
        self.setFixedSize(480, 650)
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # إنشاء الحاوية الرئيسية
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء الإطار الرئيسي الجميل
        self.main_frame = QFrame()
        self.main_frame.setObjectName("mainFrame")
        self.apply_stunning_style()

        # تخطيط الإطار
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(50, 50, 50, 50)
        frame_layout.setSpacing(30)

        # إضافة العناصر
        self.create_beautiful_header(frame_layout)
        self.create_elegant_form(frame_layout)
        self.create_modern_buttons(frame_layout)
        self.create_stylish_footer(frame_layout)

        # إضافة الإطار للحاوية
        main_layout.addWidget(self.main_frame)

        # تعيين التخطيط
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.addWidget(main_widget)
        self.setLayout(layout)

        # إضافة تأثيرات الظل المتقدمة
        self.add_advanced_shadows()

    def apply_stunning_style(self):
        """تطبيق نمط مذهل وجميل جداً"""
        self.main_frame.setStyleSheet("""
            QFrame#mainFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.95), stop:0.1 rgba(30, 41, 59, 0.9),
                    stop:0.2 rgba(51, 65, 85, 0.85), stop:0.3 rgba(71, 85, 105, 0.8),
                    stop:0.4 rgba(37, 99, 235, 0.75), stop:0.5 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.65), stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:0.8 rgba(124, 58, 237, 0.55), stop:0.9 rgba(109, 40, 217, 0.5),
                    stop:1 rgba(91, 33, 182, 0.45));
                border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 107, 107, 0.8), stop:0.1 rgba(255, 215, 0, 0.8),
                    stop:0.2 rgba(0, 255, 127, 0.8), stop:0.3 rgba(0, 191, 255, 0.8),
                    stop:0.4 rgba(255, 105, 180, 0.8), stop:0.5 rgba(147, 112, 219, 0.8),
                    stop:0.6 rgba(255, 20, 147, 0.8), stop:0.7 rgba(0, 206, 209, 0.8),
                    stop:0.8 rgba(255, 69, 0, 0.8), stop:0.9 rgba(255, 215, 0, 0.8),
                    stop:1 rgba(255, 107, 107, 0.8));
                border-radius: 30px;
                backdrop-filter: blur(20px);
            }
        """)

    def create_beautiful_header(self, layout):
        """رأس جميل ومتقدم"""
        # حاوية الرأس
        header_container = QFrame()
        header_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1), stop:0.5 rgba(255, 255, 255, 0.05),
                    stop:1 rgba(255, 255, 255, 0.02));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 20px;
                padding: 20px;
            }
        """)

        header_layout = QVBoxLayout(header_container)
        header_layout.setSpacing(15)

        # أيقونة متقدمة
        icon_label = QLabel("🚀")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            font-size: 48px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255, 215, 0, 0.3), stop:1 rgba(255, 105, 180, 0.3));
            border-radius: 35px;
            padding: 15px;
            margin: 10px;
        """)

        # العنوان الرئيسي المتقدم
        title = QLabel("Smart Finish")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #ffffff, stop:0.3 #ffd700, stop:0.6 #ff69b4, stop:1 #00bfff);
            font-size: 36px;
            font-weight: bold;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
            padding: 10px;
        """)

        # العنوان الفرعي الأنيق
        subtitle = QLabel("Advanced Management System")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-weight: 500;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
            padding: 5px;
        """)

        # رسالة الترحيب المتحركة
        welcome = QLabel("Welcome Back! • مرحباً بعودتك")
        welcome.setAlignment(Qt.AlignCenter)
        welcome.setStyleSheet("""
            color: rgba(255, 215, 0, 0.9);
            font-size: 14px;
            font-weight: 600;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
            padding: 8px;
        """)

        header_layout.addWidget(icon_label)
        header_layout.addWidget(title)
        header_layout.addWidget(subtitle)
        header_layout.addWidget(welcome)

        layout.addWidget(header_container)

    def create_elegant_form(self, layout):
        """نموذج أنيق ومتقدم"""
        # حاوية النموذج
        form_container = QFrame()
        form_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08), stop:0.5 rgba(255, 255, 255, 0.04),
                    stop:1 rgba(255, 255, 255, 0.02));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 25px;
                padding: 30px;
            }
        """)

        form_layout = QVBoxLayout(form_container)
        form_layout.setSpacing(20)

        # حقل اسم المستخدم المتقدم
        username_container = QFrame()
        username_container.setStyleSheet("QFrame { background: transparent; }")
        username_layout = QVBoxLayout(username_container)
        username_layout.setSpacing(8)

        username_label = QLabel("👤 Username • اسم المستخدم")
        username_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 600;
            font-family: 'Segoe UI';
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        """)

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Enter your username...")
        self.username_input.setText("admin")
        self.username_input.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95), stop:1 rgba(248, 250, 252, 0.9));
                border: 3px solid rgba(59, 130, 246, 0.3);
                border-radius: 15px;
                padding: 15px 20px;
                font-size: 16px;
                font-family: 'Segoe UI';
                color: #1e293b;
                font-weight: 500;
            }
            QLineEdit:focus {
                border: 3px solid rgba(59, 130, 246, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0), stop:1 rgba(248, 250, 252, 0.95));
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
            }
            QLineEdit:hover {
                border: 3px solid rgba(59, 130, 246, 0.5);
            }
        """)

        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)

        # حقل كلمة المرور المتقدم
        password_container = QFrame()
        password_container.setStyleSheet("QFrame { background: transparent; }")
        password_layout = QVBoxLayout(password_container)
        password_layout.setSpacing(8)

        password_label = QLabel("🔒 Password • كلمة المرور")
        password_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 600;
            font-family: 'Segoe UI';
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        """)

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Enter your password...")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText("admin")
        self.password_input.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95), stop:1 rgba(248, 250, 252, 0.9));
                border: 3px solid rgba(139, 92, 246, 0.3);
                border-radius: 15px;
                padding: 15px 20px;
                font-size: 16px;
                font-family: 'Segoe UI';
                color: #1e293b;
                font-weight: 500;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0), stop:1 rgba(248, 250, 252, 0.95));
                box-shadow: 0 0 20px rgba(139, 92, 246, 0.4);
            }
            QLineEdit:hover {
                border: 3px solid rgba(139, 92, 246, 0.5);
            }
        """)

        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)

        # خانة التذكر الأنيقة
        self.remember_checkbox = QCheckBox("✨ Remember Me • تذكرني")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: rgba(255, 215, 0, 0.9);
                font-size: 14px;
                font-family: 'Segoe UI';
                font-weight: 600;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 22px;
                height: 22px;
                border-radius: 8px;
                border: 3px solid rgba(255, 215, 0, 0.6);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9), stop:1 rgba(248, 250, 252, 0.8));
            }
            QCheckBox::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 215, 0, 0.9), stop:1 rgba(255, 165, 0, 0.8));
                border: 3px solid rgba(255, 215, 0, 0.9);
            }
            QCheckBox::indicator:hover {
                border: 3px solid rgba(255, 215, 0, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0), stop:1 rgba(248, 250, 252, 0.9));
            }
        """)

        form_layout.addWidget(username_container)
        form_layout.addWidget(password_container)
        form_layout.addWidget(self.remember_checkbox)

        layout.addWidget(form_container)

        # ربط Enter
        self.username_input.returnPressed.connect(self.login)
        self.password_input.returnPressed.connect(self.login)

    def create_modern_buttons(self, layout):
        """أزرار عصرية ومتقدمة"""
        # حاوية الأزرار
        buttons_container = QFrame()
        buttons_container.setStyleSheet("QFrame { background: transparent; }")
        buttons_layout = QVBoxLayout(buttons_container)
        buttons_layout.setSpacing(15)

        # زر تسجيل الدخول المتقدم
        self.login_button = QPushButton("🚀 LOGIN • دخول")
        self.login_button.clicked.connect(self.login)
        self.login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9), stop:0.3 rgba(37, 99, 235, 0.8),
                    stop:0.7 rgba(29, 78, 216, 0.7), stop:1 rgba(30, 64, 175, 0.6));
                border: 3px solid rgba(59, 130, 246, 0.8);
                border-radius: 18px;
                padding: 18px 30px;
                color: #ffffff;
                font-weight: 700;
                font-size: 18px;
                font-family: 'Segoe UI';
                min-height: 55px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 150, 255, 1.0), stop:0.3 rgba(59, 130, 246, 0.9),
                    stop:0.7 rgba(37, 99, 235, 0.8), stop:1 rgba(29, 78, 216, 0.7));
                border: 3px solid rgba(79, 150, 255, 1.0);
                transform: translateY(-3px);
                box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8), stop:0.3 rgba(29, 78, 216, 0.7),
                    stop:0.7 rgba(30, 64, 175, 0.6), stop:1 rgba(30, 58, 138, 0.5));
                transform: translateY(0px);
                box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
            }
        """)

        # زر الإلغاء الأنيق
        self.cancel_button = QPushButton("❌ CANCEL • إلغاء")
        self.cancel_button.clicked.connect(self.close)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.8), stop:0.3 rgba(220, 38, 38, 0.7),
                    stop:0.7 rgba(185, 28, 28, 0.6), stop:1 rgba(153, 27, 27, 0.5));
                border: 3px solid rgba(239, 68, 68, 0.7);
                border-radius: 18px;
                padding: 18px 30px;
                color: #ffffff;
                font-weight: 700;
                font-size: 18px;
                font-family: 'Segoe UI';
                min-height: 55px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(248, 113, 113, 0.9), stop:0.3 rgba(239, 68, 68, 0.8),
                    stop:0.7 rgba(220, 38, 38, 0.7), stop:1 rgba(185, 28, 28, 0.6));
                border: 3px solid rgba(248, 113, 113, 0.9);
                transform: translateY(-3px);
                box-shadow: 0 8px 25px rgba(239, 68, 68, 0.5);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(220, 38, 38, 0.7), stop:0.3 rgba(185, 28, 28, 0.6),
                    stop:0.7 rgba(153, 27, 27, 0.5), stop:1 rgba(127, 29, 29, 0.4));
                transform: translateY(0px);
                box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
            }
        """)

        buttons_layout.addWidget(self.login_button)
        buttons_layout.addWidget(self.cancel_button)
        layout.addWidget(buttons_container)

    def create_stylish_footer(self, layout):
        """تذييل أنيق ومتطور"""
        footer_container = QFrame()
        footer_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.05), stop:0.5 rgba(255, 255, 255, 0.02),
                    stop:1 rgba(255, 255, 255, 0.05));
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 15px;
            }
        """)

        footer_layout = QVBoxLayout(footer_container)
        footer_layout.setSpacing(5)

        # النص الرئيسي
        main_text = QLabel("© 2024 Smart Finish")
        main_text.setAlignment(Qt.AlignCenter)
        main_text.setStyleSheet("""
            color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #ffd700, stop:0.5 #ff69b4, stop:1 #00bfff);
            font-size: 14px;
            font-family: 'Segoe UI';
            font-weight: 600;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
        """)

        # النص الفرعي
        sub_text = QLabel("Advanced Management System")
        sub_text.setAlignment(Qt.AlignCenter)
        sub_text.setStyleSheet("""
            color: rgba(255, 255, 255, 0.6);
            font-size: 11px;
            font-family: 'Segoe UI';
            font-weight: 400;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
        """)

        footer_layout.addWidget(main_text)
        footer_layout.addWidget(sub_text)
        layout.addWidget(footer_container)

    def add_advanced_shadows(self):
        """إضافة ظلال متقدمة وجميلة"""
        try:
            # ظل خارجي للإطار الرئيسي
            outer_shadow = QGraphicsDropShadowEffect()
            outer_shadow.setBlurRadius(40)
            outer_shadow.setXOffset(0)
            outer_shadow.setYOffset(15)
            outer_shadow.setColor(QColor(0, 0, 0, 120))
            self.main_frame.setGraphicsEffect(outer_shadow)
        except:
            pass

    def setup_animations(self):
        """رسوم متحركة متقدمة وجميلة"""
        try:
            # رسوم متحركة للظهور
            self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
            self.fade_animation.setDuration(1000)
            self.fade_animation.setStartValue(0.0)
            self.fade_animation.setEndValue(1.0)
            self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

            # رسوم متحركة للحجم
            self.scale_animation = QPropertyAnimation(self.main_frame, b"geometry")
            self.scale_animation.setDuration(800)
            self.scale_animation.setEasingCurve(QEasingCurve.OutBack)

            # بدء الرسوم المتحركة
            QTimer.singleShot(100, self.start_animations)
        except:
            pass

    def start_animations(self):
        """بدء الرسوم المتحركة"""
        try:
            self.fade_animation.start()

            # تحريك الإطار من الأعلى
            start_rect = self.main_frame.geometry()
            start_rect.moveTop(-200)
            end_rect = QRect(20, 20, 440, 610)

            self.scale_animation.setStartValue(start_rect)
            self.scale_animation.setEndValue(end_rect)
            self.scale_animation.start()
        except:
            pass

    def login(self):
        """تسجيل دخول متقدم وجميل"""
        try:
            username = self.username_input.text().strip()
            password = self.password_input.text().strip()

            if not username or not password:
                self.show_beautiful_message("⚠️ Warning", "Please fill all fields\nيرجى ملء جميع الحقول", "warning")
                return

            # تأثير تحميل على الزر
            self.login_button.setText("🔄 Loading...")
            self.login_button.setEnabled(False)

            # محاكاة تأخير للتأثير
            QTimer.singleShot(800, self.perform_login)

        except Exception as e:
            self.show_beautiful_message("❌ Error", f"Login failed\nفشل تسجيل الدخول: {str(e)}", "error")
            self.reset_login_button()

    def perform_login(self):
        """تنفيذ عملية تسجيل الدخول"""
        try:
            username = self.username_input.text().strip()
            password = self.password_input.text().strip()

            self.session = get_session()
            user = self.session.query(User).filter_by(username=username).first()

            if user and user.password == password:
                self.user = user
                self.login_button.setText("✅ Success!")
                self.show_beautiful_message("🎉 Success", "Login successful!\nتم تسجيل الدخول بنجاح", "success")
                self.login_successful.emit(self.session, self.user)
                QTimer.singleShot(1200, self.accept)
            else:
                self.show_beautiful_message("❌ Error", "Invalid credentials\nبيانات غير صحيحة", "error")
                self.reset_login_button()

        except Exception as e:
            self.show_beautiful_message("❌ Error", f"Login failed\nفشل تسجيل الدخول: {str(e)}", "error")
            self.reset_login_button()

    def reset_login_button(self):
        """إعادة تعيين زر تسجيل الدخول"""
        self.login_button.setText("🚀 LOGIN • دخول")
        self.login_button.setEnabled(True)

    def show_beautiful_message(self, title, message, msg_type="info"):
        """رسائل جميلة ومتقدمة"""
        try:
            msg = QMessageBox(self)
            msg.setWindowTitle(title)
            msg.setText(message)

            # تحديد الألوان حسب نوع الرسالة
            if msg_type == "success":
                bg_color = "stop:0 rgba(16, 185, 129, 0.9), stop:1 rgba(5, 150, 105, 0.8)"
                border_color = "rgba(16, 185, 129, 0.8)"
            elif msg_type == "error":
                bg_color = "stop:0 rgba(239, 68, 68, 0.9), stop:1 rgba(220, 38, 38, 0.8)"
                border_color = "rgba(239, 68, 68, 0.8)"
            elif msg_type == "warning":
                bg_color = "stop:0 rgba(245, 158, 11, 0.9), stop:1 rgba(217, 119, 6, 0.8)"
                border_color = "rgba(245, 158, 11, 0.8)"
            else:
                bg_color = "stop:0 rgba(59, 130, 246, 0.9), stop:1 rgba(37, 99, 235, 0.8)"
                border_color = "rgba(59, 130, 246, 0.8)"

            msg.setStyleSheet(f"""
                QMessageBox {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, {bg_color});
                    border: 3px solid {border_color};
                    border-radius: 20px;
                    font-family: 'Segoe UI';
                    min-width: 300px;
                    min-height: 150px;
                }}
                QMessageBox QLabel {{
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: 600;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 20px;
                }}
                QMessageBox QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9), stop:1 rgba(248, 250, 252, 0.8));
                    border: 2px solid rgba(255, 255, 255, 0.8);
                    border-radius: 12px;
                    padding: 12px 24px;
                    color: #1e293b;
                    font-weight: 700;
                    font-size: 14px;
                    min-width: 100px;
                }}
                QMessageBox QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 1.0), stop:1 rgba(248, 250, 252, 0.9));
                    border: 2px solid rgba(255, 255, 255, 1.0);
                    transform: translateY(-2px);
                }}
            """)
            msg.exec_()
        except:
            pass

    def mousePressEvent(self, event):
        """سحب النافذة بسلاسة"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """تحريك النافذة بسلاسة"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()


def main():
    """اختبار شاشة الدخول الجميلة والمتقدمة"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    app.setFont(QFont("Segoe UI", 11))

    # تحسين جودة الرسم
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    print("🚀 Starting Beautiful Login Screen...")

    login = LoginScreen()
    result = login.exec_()

    if result == QDialog.Accepted and login.user:
        print(f"🎉 Login successful!")
        print(f"   User: {login.user.full_name}")
        print(f"   Role: {login.user.role}")
    else:
        print("❌ Login cancelled or failed")


if __name__ == "__main__":
    main()
