"""
شاشة دخول متطورة للبرنامج
تتطابق مع نمط الألوان والتصميم المتقدم للبرنامج الرئيسي
"""

import sys
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame, QApplication,
                            QGraphicsDropShadowEffect, QCheckBox, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor, QLinearGradient, QPen
from database import get_session, User
import hashlib

class LoginScreen(QDialog):
    """شاشة دخول متطورة مع تصميم متقدم"""
    
    # إشارة لإرسال بيانات المستخدم عند نجاح تسجيل الدخول
    login_successful = pyqtSignal(object, object)  # session, user
    
    def __init__(self):
        super().__init__()
        self.session = None
        self.user = None
        self.setup_ui()
        self.setup_animations()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - برنامج المحاسبة الإداري")
        self.setFixedSize(450, 600)
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(0)
        
        # إنشاء الإطار الرئيسي
        self.main_frame = QFrame()
        self.main_frame.setObjectName("mainFrame")
        self.apply_main_frame_style()
        
        # تخطيط الإطار الرئيسي
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(40, 40, 40, 40)
        frame_layout.setSpacing(25)
        
        # إضافة العناصر
        self.create_header(frame_layout)
        self.create_login_form(frame_layout)
        self.create_buttons(frame_layout)
        self.create_footer(frame_layout)
        
        # إضافة الإطار للتخطيط الرئيسي
        main_layout.addWidget(self.main_frame)
        self.setLayout(main_layout)
        
        # إضافة تأثير الظل
        self.add_shadow_effect()
        
    def apply_main_frame_style(self):
        """تطبيق نمط الإطار الرئيسي"""
        self.main_frame.setStyleSheet("""
            QFrame#mainFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:0.1 #f8fafc, stop:0.2 #f1f5f9,
                    stop:0.3 #e2e8f0, stop:0.4 #cbd5e1, stop:0.5 #94a3b8,
                    stop:0.6 #64748b, stop:0.7 #475569, stop:0.8 #334155,
                    stop:0.9 #1e293b, stop:1 #0f172a);
                border: 3px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3B82F6, stop:0.25 #8B5CF6, stop:0.5 #EC4899,
                    stop:0.75 #F59E0B, stop:1 #10B981);
                border-radius: 25px;
            }
        """)
        
    def create_header(self, layout):
        """إنشاء رأس الشاشة"""
        # العنوان الرئيسي
        title_label = QLabel("برنامج المحاسبة الإداري")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        
        # العنوان الفرعي
        subtitle_label = QLabel("Smart Finish")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setObjectName("subtitleLabel")
        
        # رسالة الترحيب
        welcome_label = QLabel("مرحباً بك، يرجى تسجيل الدخول")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setObjectName("welcomeLabel")
        
        # تطبيق الأنماط
        self.apply_header_styles(title_label, subtitle_label, welcome_label)
        
        layout.addWidget(title_label)
        layout.addWidget(subtitle_label)
        layout.addSpacing(20)
        layout.addWidget(welcome_label)
        
    def apply_header_styles(self, title_label, subtitle_label, welcome_label):
        """تطبيق أنماط الرأس"""
        title_label.setStyleSheet("""
            QLabel#titleLabel {
                color: #ffffff;
                font-size: 28px;
                font-weight: bold;
                font-family: 'Arial';
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8), stop:0.5 rgba(139, 92, 246, 0.8), stop:1 rgba(236, 72, 153, 0.8));
                border-radius: 15px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)
        
        subtitle_label.setStyleSheet("""
            QLabel#subtitleLabel {
                color: #10B981;
                font-size: 22px;
                font-weight: bold;
                font-family: 'Arial';
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
                padding: 5px;
            }
        """)
        
        welcome_label.setStyleSheet("""
            QLabel#welcomeLabel {
                color: #E2E8F0;
                font-size: 16px;
                font-weight: 500;
                font-family: 'Arial';
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            }
        """)
        
    def create_login_form(self, layout):
        """إنشاء نموذج تسجيل الدخول"""
        # إطار النموذج
        form_frame = QFrame()
        form_frame.setObjectName("formFrame")
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(15)
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setObjectName("fieldLabel")
        self.username_input = QLineEdit()
        self.username_input.setObjectName("inputField")
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setText("admin")  # قيمة افتراضية
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setObjectName("fieldLabel")
        self.password_input = QLineEdit()
        self.password_input.setObjectName("inputField")
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText("admin")  # قيمة افتراضية
        
        # خانة اختيار "تذكرني"
        self.remember_checkbox = QCheckBox("تذكرني")
        self.remember_checkbox.setObjectName("rememberCheckbox")
        
        # تطبيق الأنماط
        self.apply_form_styles(form_frame, username_label, password_label)
        
        # إضافة العناصر للنموذج
        form_layout.addWidget(username_label)
        form_layout.addWidget(self.username_input)
        form_layout.addWidget(password_label)
        form_layout.addWidget(self.password_input)
        form_layout.addWidget(self.remember_checkbox)
        
        layout.addWidget(form_frame)
        
        # ربط Enter بتسجيل الدخول
        self.username_input.returnPressed.connect(self.login)
        self.password_input.returnPressed.connect(self.login)
        
    def apply_form_styles(self, form_frame, username_label, password_label):
        """تطبيق أنماط النموذج"""
        form_frame.setStyleSheet("""
            QFrame#formFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 20px;
            }
        """)
        
        label_style = """
            QLabel#fieldLabel {
                color: #E2E8F0;
                font-size: 16px;
                font-weight: 600;
                font-family: 'Arial';
                margin-bottom: 5px;
            }
        """
        username_label.setStyleSheet(label_style)
        password_label.setStyleSheet(label_style)
        
        input_style = """
            QLineEdit#inputField {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid #CBD5E1;
                border-radius: 10px;
                padding: 12px 15px;
                font-size: 16px;
                font-family: 'Arial';
                color: #1E293B;
            }
            QLineEdit#inputField:focus {
                border: 2px solid #3B82F6;
                background: rgba(255, 255, 255, 1.0);
                box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
            }
        """
        self.username_input.setStyleSheet(input_style)
        self.password_input.setStyleSheet(input_style)
        
        self.remember_checkbox.setStyleSheet("""
            QCheckBox#rememberCheckbox {
                color: #E2E8F0;
                font-size: 14px;
                font-family: 'Arial';
                spacing: 8px;
            }
            QCheckBox#rememberCheckbox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 4px;
                border: 2px solid #CBD5E1;
                background: rgba(255, 255, 255, 0.9);
            }
            QCheckBox#rememberCheckbox::indicator:checked {
                background: #3B82F6;
                border: 2px solid #3B82F6;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjIgNy44TDEuNCA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
        """)

    def create_buttons(self, layout):
        """إنشاء الأزرار"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)

        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.login)

        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setObjectName("cancelButton")
        self.cancel_button.clicked.connect(self.close)

        # تطبيق الأنماط المتطورة
        self.style_advanced_button(self.login_button, 'primary')
        self.style_advanced_button(self.cancel_button, 'danger')

        buttons_layout.addWidget(self.login_button)
        buttons_layout.addWidget(self.cancel_button)

        layout.addWidget(buttons_frame)

    def create_footer(self, layout):
        """إنشاء تذييل الشاشة"""
        footer_label = QLabel("© 2024 Smart Finish - جميع الحقوق محفوظة")
        footer_label.setAlignment(Qt.AlignCenter)
        footer_label.setObjectName("footerLabel")
        footer_label.setStyleSheet("""
            QLabel#footerLabel {
                color: #94A3B8;
                font-size: 12px;
                font-family: 'Arial';
                margin-top: 20px;
            }
        """)

        layout.addWidget(footer_label)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار مطابق للبرنامج الرئيسي"""
        try:
            # تحديد الألوان حسب نوع الزر
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#8f2d2d', 'hover_mid': '#a92b2b', 'hover_end': '#c92c2c', 'hover_bottom': '#ec3636',
                    'hover_border': '#ef4444', 'pressed_start': '#6f0d0d', 'pressed_mid': '#890b0b',
                    'pressed_end': '#a90c0c', 'pressed_bottom': '#cc1616', 'pressed_border': '#dc2626',
                    'border': '#ef4444', 'text': '#ffffff', 'shadow': 'rgba(239, 68, 68, 0.5)'
                }
            }

            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق النمط المتطور
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['bg_start']}, stop:0.3 {color_scheme['bg_mid']},
                        stop:0.7 {color_scheme['bg_end']}, stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 2px solid {color_scheme['border']};
                    border-radius: 12px;
                    padding: 12px 24px;
                    font-size: 16px;
                    font-weight: bold;
                    font-family: 'Arial';
                    min-height: 45px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['hover_start']}, stop:0.3 {color_scheme['hover_mid']},
                        stop:0.7 {color_scheme['hover_end']}, stop:1 {color_scheme['hover_bottom']});
                    border: 2px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 6px 20px {color_scheme['shadow']};
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_scheme['pressed_start']}, stop:0.3 {color_scheme['pressed_mid']},
                        stop:0.7 {color_scheme['pressed_end']}, stop:1 {color_scheme['pressed_bottom']});
                    border: 2px solid {color_scheme['pressed_border']};
                    transform: translateY(0px);
                    box-shadow: 0 4px 15px {color_scheme['shadow']};
                }}
            """)

        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def add_shadow_effect(self):
        """إضافة تأثير الظل للإطار الرئيسي"""
        try:
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(30)
            shadow.setXOffset(0)
            shadow.setYOffset(10)
            shadow.setColor(QColor(0, 0, 0, 100))
            self.main_frame.setGraphicsEffect(shadow)
        except Exception as e:
            print(f"خطأ في إضافة تأثير الظل: {e}")

    def setup_animations(self):
        """إعداد الرسوم المتحركة"""
        try:
            # رسوم متحركة لظهور النافذة
            self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
            self.fade_animation.setDuration(800)
            self.fade_animation.setStartValue(0.0)
            self.fade_animation.setEndValue(1.0)
            self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

            # بدء الرسوم المتحركة عند الإظهار
            QTimer.singleShot(100, self.start_fade_in)

        except Exception as e:
            print(f"خطأ في إعداد الرسوم المتحركة: {e}")

    def start_fade_in(self):
        """بدء رسوم متحركة للظهور"""
        try:
            self.fade_animation.start()
        except Exception as e:
            print(f"خطأ في بدء الرسوم المتحركة: {e}")

    def login(self):
        """تسجيل الدخول"""
        try:
            username = self.username_input.text().strip()
            password = self.password_input.text().strip()

            if not username or not password:
                self.show_error_message("يرجى إدخال اسم المستخدم وكلمة المرور")
                return

            # إنشاء جلسة قاعدة البيانات
            self.session = get_session()

            # البحث عن المستخدم
            user = self.session.query(User).filter_by(username=username).first()

            if user and user.password == password:
                self.user = user
                self.show_success_message("تم تسجيل الدخول بنجاح!")

                # إرسال إشارة نجاح تسجيل الدخول
                self.login_successful.emit(self.session, self.user)

                # إغلاق شاشة الدخول
                QTimer.singleShot(1000, self.accept)

            else:
                self.show_error_message("اسم المستخدم أو كلمة المرور غير صحيحة")

        except Exception as e:
            self.show_error_message(f"حدث خطأ أثناء تسجيل الدخول: {str(e)}")
            print(f"خطأ في تسجيل الدخول: {e}")

    def show_error_message(self, message):
        """عرض رسالة خطأ"""
        try:
            msg_box = QMessageBox(self)
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle("خطأ")
            msg_box.setText(message)
            msg_box.setStyleSheet(self.get_message_box_style())
            msg_box.exec_()
        except Exception as e:
            print(f"خطأ في عرض رسالة الخطأ: {e}")

    def show_success_message(self, message):
        """عرض رسالة نجاح"""
        try:
            msg_box = QMessageBox(self)
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setWindowTitle("نجح")
            msg_box.setText(message)
            msg_box.setStyleSheet(self.get_message_box_style())
            msg_box.exec_()
        except Exception as e:
            print(f"خطأ في عرض رسالة النجاح: {e}")

    def get_message_box_style(self):
        """الحصول على نمط صندوق الرسائل"""
        return """
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:1 #e2e8f0);
                border: 2px solid #3B82F6;
                border-radius: 10px;
                font-family: 'Arial';
            }
            QMessageBox QLabel {
                color: #1E293B;
                font-size: 14px;
                font-weight: 500;
            }
            QMessageBox QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3B82F6, stop:1 #2563EB);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563EB, stop:1 #1D4ED8);
            }
        """

    def mousePressEvent(self, event):
        """التعامل مع النقر لسحب النافذة"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """التعامل مع سحب النافذة"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()


def main():
    """دالة اختبار شاشة الدخول"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # تعيين الخط
    font = QFont("Arial", 10)
    app.setFont(font)

    # إنشاء شاشة الدخول
    login_screen = LoginScreen()

    # عرض الشاشة
    if login_screen.exec_() == QDialog.Accepted:
        print("تم تسجيل الدخول بنجاح!")
        print(f"المستخدم: {login_screen.user.full_name if login_screen.user else 'غير محدد'}")
    else:
        print("تم إلغاء تسجيل الدخول")

    sys.exit()


if __name__ == "__main__":
    main()
