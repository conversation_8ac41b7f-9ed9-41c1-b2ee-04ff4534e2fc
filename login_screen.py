"""
شاشة دخول عالمية متطورة - مختصرة ومحسنة
"""

import sys
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QLabel, QLineEdit,
                            QPushButton, QApplication, QMessageBox, QCheckBox)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt5.QtGui import QFont
from database import get_session, User

class LoginScreen(QDialog):
    """شاشة دخول عالمية متطورة"""

    login_successful = pyqtSignal(object, object)

    def __init__(self):
        super().__init__()
        self.session = None
        self.user = None
        self.setup_ui()
        self.setup_animations()

    def setup_ui(self):
        """إعداد واجهة مختصرة وعالمية"""
        self.setWindowTitle("Smart Finish - Login")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # تطبيق النمط العالمي مباشرة على النافذة
        self.apply_global_style()

        # التخطيط الرئيسي
        layout = QVBoxLayout()
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(20)

        # العناصر
        self.create_header(layout)
        self.create_form(layout)
        self.create_buttons(layout)
        self.create_footer(layout)

        self.setLayout(layout)

    def apply_global_style(self):
        """تطبيق النمط العالمي المتطور"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E293B, stop:0.4 #2563EB,
                    stop:0.6 #8B5CF6, stop:0.8 #7C3AED, stop:1 #5B21B6);
                border: 3px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff6b6b, stop:0.25 #ffd700, stop:0.5 #00ff7f,
                    stop:0.75 #00bfff, stop:1 #ff69b4);
                border-radius: 20px;
            }
        """)

    def create_header(self, layout):
        """رأس مختصر وعالمي"""
        # العنوان الرئيسي
        title = QLabel("Smart Finish")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            color: #ffffff; font-size: 32px; font-weight: bold;
            font-family: 'Segoe UI'; text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            padding: 15px; margin-bottom: 10px;
        """)

        # رسالة الترحيب
        welcome = QLabel("Welcome - مرحباً بك")
        welcome.setAlignment(Qt.AlignCenter)
        welcome.setStyleSheet("""
            color: #ffd700; font-size: 16px; font-weight: 500;
            font-family: 'Segoe UI'; text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        """)

        layout.addWidget(title)
        layout.addWidget(welcome)

    def create_form(self, layout):
        """نموذج مختصر وعالمي"""
        # حقول الإدخال
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Username / اسم المستخدم")
        self.username_input.setText("admin")

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Password / كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText("admin")

        self.remember_checkbox = QCheckBox("Remember Me / تذكرني")

        # تطبيق الأنماط
        input_style = """
            QLineEdit {
                background: rgba(255,255,255,0.9); border: 2px solid rgba(120,120,120,0.6);
                border-radius: 12px; padding: 12px 15px; font-size: 16px;
                font-family: 'Segoe UI'; color: #0F172A; font-weight: 500;
            }
            QLineEdit:focus {
                border: 2px solid #3B82F6; background: rgba(255,255,255,1.0);
                box-shadow: 0 0 15px rgba(59,130,246,0.4);
            }
        """

        checkbox_style = """
            QCheckBox {
                color: #f1f5f9; font-size: 14px; font-family: 'Segoe UI';
                font-weight: 500; text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
            }
            QCheckBox::indicator {
                width: 18px; height: 18px; border-radius: 4px;
                border: 2px solid rgba(120,120,120,0.6); background: rgba(255,255,255,0.9);
            }
            QCheckBox::indicator:checked {
                background: #3B82F6; border: 2px solid #3B82F6;
            }
        """

        self.username_input.setStyleSheet(input_style)
        self.password_input.setStyleSheet(input_style)
        self.remember_checkbox.setStyleSheet(checkbox_style)

        layout.addWidget(self.username_input)
        layout.addWidget(self.password_input)
        layout.addWidget(self.remember_checkbox)

        # ربط Enter
        self.username_input.returnPressed.connect(self.login)
        self.password_input.returnPressed.connect(self.login)

    def create_buttons(self, layout):
        """أزرار مختصرة وعالمية"""
        # زر تسجيل الدخول
        self.login_button = QPushButton("Login / دخول")
        self.login_button.clicked.connect(self.login)

        # زر الإلغاء
        self.cancel_button = QPushButton("Cancel / إلغاء")
        self.cancel_button.clicked.connect(self.close)

        # نمط موحد للأزرار
        button_style = """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(100,100,100,0.4), stop:1 rgba(40,40,40,0.2));
                border: 2px solid rgba(120,120,120,0.6); border-radius: 12px;
                padding: 16px 24px; color: #f1f5f9; font-weight: 700;
                font-size: 16px; font-family: 'Segoe UI'; min-height: 45px;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(120,120,120,0.5), stop:1 rgba(60,60,60,0.3));
                border: 2px solid rgba(140,140,140,0.8);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(80,80,80,0.3), stop:1 rgba(20,20,20,0.15));
                transform: translateY(0px);
            }
        """

        # نمط خاص للزر الأحمر
        cancel_style = button_style.replace("rgba(100,100,100,0.4)", "rgba(127,29,29,0.4)")
        cancel_style = cancel_style.replace("rgba(40,40,40,0.2)", "rgba(220,38,38,0.2)")
        cancel_style = cancel_style.replace("rgba(120,120,120,0.6)", "rgba(239,68,68,0.6)")

        self.login_button.setStyleSheet(button_style)
        self.cancel_button.setStyleSheet(cancel_style)

        layout.addWidget(self.login_button)
        layout.addWidget(self.cancel_button)

    def create_footer(self, layout):
        """تذييل مختصر"""
        footer = QLabel("© 2024 Smart Finish")
        footer.setAlignment(Qt.AlignCenter)
        footer.setStyleSheet("""
            color: #ffd700; font-size: 12px; font-family: 'Segoe UI';
            font-weight: 500; text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        """)
        layout.addWidget(footer)

    def setup_animations(self):
        """رسوم متحركة مختصرة"""
        try:
            self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
            self.fade_animation.setDuration(600)
            self.fade_animation.setStartValue(0.0)
            self.fade_animation.setEndValue(1.0)
            self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
            QTimer.singleShot(50, self.fade_animation.start)
        except:
            pass

    def login(self):
        """تسجيل دخول مختصر"""
        try:
            username = self.username_input.text().strip()
            password = self.password_input.text().strip()

            if not username or not password:
                self.show_message("Error / خطأ", "Please fill all fields / يرجى ملء جميع الحقول")
                return

            self.session = get_session()
            user = self.session.query(User).filter_by(username=username).first()

            if user and user.password == password:
                self.user = user
                self.show_message("Success / نجح", "Login successful / تم تسجيل الدخول بنجاح")
                self.login_successful.emit(self.session, self.user)
                QTimer.singleShot(800, self.accept)
            else:
                self.show_message("Error / خطأ", "Invalid credentials / بيانات غير صحيحة")

        except Exception as e:
            self.show_message("Error / خطأ", f"Login failed / فشل تسجيل الدخول: {str(e)}")

    def show_message(self, title, message):
        """رسائل مختصرة وعالمية"""
        try:
            msg = QMessageBox(self)
            msg.setWindowTitle(title)
            msg.setText(message)
            msg.setStyleSheet("""
                QMessageBox {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.4 #2563EB, stop:0.8 #7C3AED, stop:1 #5B21B6);
                    border: 2px solid rgba(255,255,255,0.3); border-radius: 15px;
                    font-family: 'Segoe UI';
                }
                QMessageBox QLabel { color: #f1f5f9; font-size: 14px; font-weight: 500; }
                QMessageBox QPushButton {
                    background: rgba(100,100,100,0.4); border: 2px solid rgba(120,120,120,0.6);
                    border-radius: 8px; padding: 8px 16px; color: #f1f5f9;
                    font-weight: 700; min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background: rgba(120,120,120,0.5); border: 2px solid rgba(140,140,140,0.8);
                }
            """)
            msg.exec_()
        except:
            pass

    def mousePressEvent(self, event):
        """سحب النافذة"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()

    def mouseMoveEvent(self, event):
        """تحريك النافذة"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)


def main():
    """اختبار سريع"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    app.setFont(QFont("Segoe UI", 10))

    login = LoginScreen()
    if login.exec_() == QDialog.Accepted:
        print(f"✅ Login successful: {login.user.full_name if login.user else 'Unknown'}")
    else:
        print("❌ Login cancelled")


if __name__ == "__main__":
    main()
