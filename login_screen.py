"""
شاشة دخول متطورة للبرنامج
تتطابق مع نمط الألوان والتصميم المتقدم للبرنامج الرئيسي
"""

import sys
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame, QApplication,
                            QGraphicsDropShadowEffect, QCheckBox, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor, QLinearGradient, QPen
from database import get_session, User
import hashlib

class LoginScreen(QDialog):
    """شاشة دخول متطورة مع تصميم متقدم"""
    
    # إشارة لإرسال بيانات المستخدم عند نجاح تسجيل الدخول
    login_successful = pyqtSignal(object, object)  # session, user
    
    def __init__(self):
        super().__init__()
        self.session = None
        self.user = None
        self.setup_ui()
        self.setup_animations()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - برنامج المحاسبة الإداري")
        self.setFixedSize(450, 600)
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(0)
        
        # إنشاء الإطار الرئيسي
        self.main_frame = QFrame()
        self.main_frame.setObjectName("mainFrame")
        self.apply_main_frame_style()
        
        # تخطيط الإطار الرئيسي
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(40, 40, 40, 40)
        frame_layout.setSpacing(25)
        
        # إضافة العناصر
        self.create_header(frame_layout)
        self.create_login_form(frame_layout)
        self.create_buttons(frame_layout)
        self.create_footer(frame_layout)
        
        # إضافة الإطار للتخطيط الرئيسي
        main_layout.addWidget(self.main_frame)
        self.setLayout(main_layout)
        
        # إضافة تأثير الظل
        self.add_shadow_effect()
        
    def apply_main_frame_style(self):
        """تطبيق نمط الإطار الرئيسي مطابق للبرنامج"""
        self.main_frame.setStyleSheet("""
            QFrame#mainFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff6b6b, stop:0.15 #ffd700, stop:0.3 #00ff7f,
                    stop:0.45 #00bfff, stop:0.6 #ff69b4, stop:0.75 #9370db,
                    stop:0.9 #ffd700, stop:1 #ff6b6b);
                border-radius: 25px;
            }
        """)
        
    def create_header(self, layout):
        """إنشاء رأس الشاشة"""
        # العنوان الرئيسي
        title_label = QLabel("برنامج المحاسبة الإداري")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        
        # العنوان الفرعي
        subtitle_label = QLabel("Smart Finish")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setObjectName("subtitleLabel")
        
        # رسالة الترحيب
        welcome_label = QLabel("مرحباً بك، يرجى تسجيل الدخول")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setObjectName("welcomeLabel")
        
        # تطبيق الأنماط
        self.apply_header_styles(title_label, subtitle_label, welcome_label)
        
        layout.addWidget(title_label)
        layout.addWidget(subtitle_label)
        layout.addSpacing(20)
        layout.addWidget(welcome_label)
        
    def apply_header_styles(self, title_label, subtitle_label, welcome_label):
        """تطبيق أنماط الرأس مطابقة للبرنامج"""
        title_label.setStyleSheet("""
            QLabel#titleLabel {
                color: #ffffff;
                font-size: 28px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3B82F6, stop:0.1 #2563EB, stop:0.2 #1E40AF,
                    stop:0.3 #1E293B, stop:0.4 #0F172A, stop:0.5 #000000,
                    stop:0.6 #0F172A, stop:0.7 #1E293B, stop:0.8 #1E40AF,
                    stop:0.9 #2563EB, stop:1 #3B82F6);
                border-radius: 15px;
                border: 2px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.3), stop:0.5 rgba(255, 255, 255, 0.5), stop:1 rgba(255, 255, 255, 0.3));
            }
        """)

        subtitle_label.setStyleSheet("""
            QLabel#subtitleLabel {
                color: #ffd700;
                font-size: 22px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 8px;
            }
        """)

        welcome_label.setStyleSheet("""
            QLabel#welcomeLabel {
                color: #f1f5f9;
                font-size: 16px;
                font-weight: 500;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            }
        """)
        
    def create_login_form(self, layout):
        """إنشاء نموذج تسجيل الدخول"""
        # إطار النموذج
        form_frame = QFrame()
        form_frame.setObjectName("formFrame")
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(15)
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setObjectName("fieldLabel")
        self.username_input = QLineEdit()
        self.username_input.setObjectName("inputField")
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setText("admin")  # قيمة افتراضية
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setObjectName("fieldLabel")
        self.password_input = QLineEdit()
        self.password_input.setObjectName("inputField")
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText("admin")  # قيمة افتراضية
        
        # خانة اختيار "تذكرني"
        self.remember_checkbox = QCheckBox("تذكرني")
        self.remember_checkbox.setObjectName("rememberCheckbox")
        
        # تطبيق الأنماط
        self.apply_form_styles(form_frame, username_label, password_label)
        
        # إضافة العناصر للنموذج
        form_layout.addWidget(username_label)
        form_layout.addWidget(self.username_input)
        form_layout.addWidget(password_label)
        form_layout.addWidget(self.password_input)
        form_layout.addWidget(self.remember_checkbox)
        
        layout.addWidget(form_frame)
        
        # ربط Enter بتسجيل الدخول
        self.username_input.returnPressed.connect(self.login)
        self.password_input.returnPressed.connect(self.login)
        
    def apply_form_styles(self, form_frame, username_label, password_label):
        """تطبيق أنماط النموذج مطابقة للبرنامج"""
        form_frame.setStyleSheet("""
            QFrame#formFrame {
                background: rgba(15, 23, 42, 0.8);
                border: 2px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.3), stop:0.5 rgba(255, 255, 255, 0.5), stop:1 rgba(255, 255, 255, 0.3));
                border-radius: 15px;
                padding: 25px;
            }
        """)

        label_style = """
            QLabel#fieldLabel {
                color: #f1f5f9;
                font-size: 16px;
                font-weight: 700;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                margin-bottom: 8px;
            }
        """
        username_label.setStyleSheet(label_style)
        password_label.setStyleSheet(label_style)

        input_style = """
            QLineEdit#inputField {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(241, 245, 249, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 2px solid rgba(120, 120, 120, 0.6);
                border-radius: 12px;
                padding: 12px 15px;
                font-size: 16px;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                color: #0F172A;
                font-weight: 500;
            }
            QLineEdit#inputField:focus {
                border: 2px solid #3B82F6;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(248, 250, 252, 0.95));
                box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
            }
        """
        self.username_input.setStyleSheet(input_style)
        self.password_input.setStyleSheet(input_style)

        self.remember_checkbox.setStyleSheet("""
            QCheckBox#rememberCheckbox {
                color: #f1f5f9;
                font-size: 14px;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                font-weight: 500;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                spacing: 10px;
            }
            QCheckBox#rememberCheckbox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 6px;
                border: 2px solid rgba(120, 120, 120, 0.6);
                background: rgba(241, 245, 249, 0.9);
            }
            QCheckBox#rememberCheckbox::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3B82F6, stop:1 #2563EB);
                border: 2px solid #3B82F6;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjIgNy44TDEuNCA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
        """)

    def create_buttons(self, layout):
        """إنشاء الأزرار"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)

        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.login)

        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setObjectName("cancelButton")
        self.cancel_button.clicked.connect(self.close)

        # تطبيق الأنماط المتطورة
        self.style_advanced_button(self.login_button, 'primary')
        self.style_advanced_button(self.cancel_button, 'danger')

        buttons_layout.addWidget(self.login_button)
        buttons_layout.addWidget(self.cancel_button)

        layout.addWidget(buttons_frame)

    def create_footer(self, layout):
        """إنشاء تذييل الشاشة"""
        footer_label = QLabel("© 2024 Smart Finish - جميع الحقوق محفوظة")
        footer_label.setAlignment(Qt.AlignCenter)
        footer_label.setObjectName("footerLabel")
        footer_label.setStyleSheet("""
            QLabel#footerLabel {
                color: #ffd700;
                font-size: 12px;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                font-weight: 500;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                margin-top: 20px;
            }
        """)

        layout.addWidget(footer_label)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار مطابق للشريط الأفقي في البرنامج الرئيسي"""
        try:
            # تحديد الألوان حسب نوع الزر - مطابق للشريط الأفقي
            if button_type == 'primary':
                # نمط زر تسجيل الدخول - مطابق لأزرار الشريط الأفقي
                button.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 rgba(100, 100, 100, 0.4),
                            stop:0.3 rgba(80, 80, 80, 0.3),
                            stop:0.7 rgba(60, 60, 60, 0.25),
                            stop:1 rgba(40, 40, 40, 0.2));
                        border: 2px solid rgba(120, 120, 120, 0.6);
                        border-radius: 12px;
                        padding: 16px 24px;
                        color: #f1f5f9;
                        font-weight: 700;
                        font-size: 16px;
                        font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                        min-height: 45px;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 rgba(120, 120, 120, 0.5),
                            stop:0.3 rgba(100, 100, 100, 0.4),
                            stop:0.7 rgba(80, 80, 80, 0.35),
                            stop:1 rgba(60, 60, 60, 0.3));
                        border: 2px solid rgba(140, 140, 140, 0.8);
                        transform: translateY(-2px);
                        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
                    }
                    QPushButton:pressed {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 rgba(80, 80, 80, 0.3),
                            stop:0.3 rgba(60, 60, 60, 0.25),
                            stop:0.7 rgba(40, 40, 40, 0.2),
                            stop:1 rgba(20, 20, 20, 0.15));
                        border: 2px solid rgba(100, 100, 100, 0.5);
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
                    }
                """)
            elif button_type == 'danger':
                # نمط زر الإلغاء - أحمر مع نفس النمط
                button.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 rgba(127, 29, 29, 0.4),
                            stop:0.3 rgba(153, 27, 27, 0.3),
                            stop:0.7 rgba(185, 28, 28, 0.25),
                            stop:1 rgba(220, 38, 38, 0.2));
                        border: 2px solid rgba(239, 68, 68, 0.6);
                        border-radius: 12px;
                        padding: 16px 24px;
                        color: #f1f5f9;
                        font-weight: 700;
                        font-size: 16px;
                        font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                        min-height: 45px;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 rgba(147, 39, 39, 0.5),
                            stop:0.3 rgba(173, 37, 37, 0.4),
                            stop:0.7 rgba(201, 44, 44, 0.35),
                            stop:1 rgba(236, 54, 54, 0.3));
                        border: 2px solid rgba(239, 68, 68, 0.8);
                        transform: translateY(-2px);
                        box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
                    }
                    QPushButton:pressed {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 rgba(107, 19, 19, 0.3),
                            stop:0.3 rgba(133, 21, 21, 0.25),
                            stop:0.7 rgba(169, 24, 24, 0.2),
                            stop:1 rgba(204, 34, 34, 0.15));
                        border: 2px solid rgba(220, 38, 38, 0.5);
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
                    }
                """)

        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def add_shadow_effect(self):
        """إضافة تأثير الظل للإطار الرئيسي"""
        try:
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(30)
            shadow.setXOffset(0)
            shadow.setYOffset(10)
            shadow.setColor(QColor(0, 0, 0, 100))
            self.main_frame.setGraphicsEffect(shadow)
        except Exception as e:
            print(f"خطأ في إضافة تأثير الظل: {e}")

    def setup_animations(self):
        """إعداد الرسوم المتحركة"""
        try:
            # رسوم متحركة لظهور النافذة
            self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
            self.fade_animation.setDuration(800)
            self.fade_animation.setStartValue(0.0)
            self.fade_animation.setEndValue(1.0)
            self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

            # بدء الرسوم المتحركة عند الإظهار
            QTimer.singleShot(100, self.start_fade_in)

        except Exception as e:
            print(f"خطأ في إعداد الرسوم المتحركة: {e}")

    def start_fade_in(self):
        """بدء رسوم متحركة للظهور"""
        try:
            self.fade_animation.start()
        except Exception as e:
            print(f"خطأ في بدء الرسوم المتحركة: {e}")

    def login(self):
        """تسجيل الدخول"""
        try:
            username = self.username_input.text().strip()
            password = self.password_input.text().strip()

            if not username or not password:
                self.show_error_message("يرجى إدخال اسم المستخدم وكلمة المرور")
                return

            # إنشاء جلسة قاعدة البيانات
            self.session = get_session()

            # البحث عن المستخدم
            user = self.session.query(User).filter_by(username=username).first()

            if user and user.password == password:
                self.user = user
                self.show_success_message("تم تسجيل الدخول بنجاح!")

                # إرسال إشارة نجاح تسجيل الدخول
                self.login_successful.emit(self.session, self.user)

                # إغلاق شاشة الدخول
                QTimer.singleShot(1000, self.accept)

            else:
                self.show_error_message("اسم المستخدم أو كلمة المرور غير صحيحة")

        except Exception as e:
            self.show_error_message(f"حدث خطأ أثناء تسجيل الدخول: {str(e)}")
            print(f"خطأ في تسجيل الدخول: {e}")

    def show_error_message(self, message):
        """عرض رسالة خطأ"""
        try:
            msg_box = QMessageBox(self)
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle("خطأ")
            msg_box.setText(message)
            msg_box.setStyleSheet(self.get_message_box_style())
            msg_box.exec_()
        except Exception as e:
            print(f"خطأ في عرض رسالة الخطأ: {e}")

    def show_success_message(self, message):
        """عرض رسالة نجاح"""
        try:
            msg_box = QMessageBox(self)
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setWindowTitle("نجح")
            msg_box.setText(message)
            msg_box.setStyleSheet(self.get_message_box_style())
            msg_box.exec_()
        except Exception as e:
            print(f"خطأ في عرض رسالة النجاح: {e}")

    def get_message_box_style(self):
        """الحصول على نمط صندوق الرسائل مطابق للبرنامج"""
        return """
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 2px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.3), stop:0.5 rgba(255, 255, 255, 0.5), stop:1 rgba(255, 255, 255, 0.3));
                border-radius: 15px;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
            }
            QMessageBox QLabel {
                color: #f1f5f9;
                font-size: 14px;
                font-weight: 500;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            }
            QMessageBox QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(100, 100, 100, 0.4),
                    stop:0.3 rgba(80, 80, 80, 0.3),
                    stop:0.7 rgba(60, 60, 60, 0.25),
                    stop:1 rgba(40, 40, 40, 0.2));
                border: 2px solid rgba(120, 120, 120, 0.6);
                border-radius: 8px;
                padding: 8px 16px;
                color: #f1f5f9;
                font-weight: 700;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                min-width: 80px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            }
            QMessageBox QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(120, 120, 120, 0.5),
                    stop:0.3 rgba(100, 100, 100, 0.4),
                    stop:0.7 rgba(80, 80, 80, 0.35),
                    stop:1 rgba(60, 60, 60, 0.3));
                border: 2px solid rgba(140, 140, 140, 0.8);
            }
        """

    def mousePressEvent(self, event):
        """التعامل مع النقر لسحب النافذة"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """التعامل مع سحب النافذة"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()


def main():
    """دالة اختبار شاشة الدخول"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # تعيين الخط
    font = QFont("Arial", 10)
    app.setFont(font)

    # إنشاء شاشة الدخول
    login_screen = LoginScreen()

    # عرض الشاشة
    if login_screen.exec_() == QDialog.Accepted:
        print("تم تسجيل الدخول بنجاح!")
        print(f"المستخدم: {login_screen.user.full_name if login_screen.user else 'غير محدد'}")
    else:
        print("تم إلغاء تسجيل الدخول")

    sys.exit()


if __name__ == "__main__":
    main()
