"""
ملف اختبار شاشة الدخول المتطورة
"""

import sys
import os
import warnings
from PyQt5.QtWidgets import QApplication, QDialog
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إعداد إخفاء التحذيرات
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'

# استيراد قاعدة البيانات وشاشة الدخول
from database import init_db, get_session, User
from login_screen import LoginScreen

def setup_test_application():
    """إعداد تطبيق الاختبار"""
    # تعيين خيارات الأداء العالي
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)
    
    # تعيين اسم التطبيق
    app.setApplicationName("اختبار شاشة الدخول")
    
    # تعيين نمط التطبيق
    app.setStyle("Fusion")
    
    # تعيين اتجاه النص من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين الخط الافتراضي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    return app

def setup_test_database():
    """إعداد قاعدة البيانات للاختبار"""
    print("🔧 إعداد قاعدة البيانات...")
    init_db()
    
    # إنشاء جلسة قاعدة البيانات
    session = get_session()
    
    # إنشاء أو الحصول على المستخدم الإداري الافتراضي
    user = session.query(User).filter_by(role="admin").first()
    if not user:
        print("🔧 إنشاء مستخدم إداري افتراضي...")
        user = User(
            username="admin",
            password="admin",
            role="admin",
            full_name="المدير العام"
        )
        session.add(user)
        session.commit()
        print("✅ تم إنشاء المستخدم الإداري بنجاح")
    
    # إنشاء مستخدم عادي للاختبار
    test_user = session.query(User).filter_by(username="test").first()
    if not test_user:
        print("🔧 إنشاء مستخدم اختبار...")
        test_user = User(
            username="test",
            password="test123",
            role="user",
            full_name="مستخدم الاختبار"
        )
        session.add(test_user)
        session.commit()
        print("✅ تم إنشاء مستخدم الاختبار بنجاح")
    
    print("✅ تم إعداد قاعدة البيانات بنجاح")
    return session

def test_login_screen():
    """اختبار شاشة الدخول"""
    try:
        print("🚀 بدء اختبار شاشة الدخول...")
        
        # إعداد التطبيق
        app = setup_test_application()
        print("✅ تم إعداد التطبيق بنجاح")
        
        # إعداد قاعدة البيانات
        session = setup_test_database()
        
        # إنشاء شاشة الدخول
        print("🔐 إنشاء شاشة الدخول...")
        login_screen = LoginScreen()
        
        # متغيرات لحفظ بيانات المستخدم
        authenticated_session = None
        authenticated_user = None
        
        def on_login_successful(session_data, user_data):
            """معالج نجاح تسجيل الدخول"""
            nonlocal authenticated_session, authenticated_user
            authenticated_session = session_data
            authenticated_user = user_data
            print(f"✅ تم تسجيل الدخول بنجاح للمستخدم: {user_data.full_name}")
            print(f"   - اسم المستخدم: {user_data.username}")
            print(f"   - الدور: {user_data.role}")
        
        # ربط إشارة نجاح تسجيل الدخول
        login_screen.login_successful.connect(on_login_successful)
        
        # عرض معلومات المستخدمين المتاحين
        print("\n📋 المستخدمين المتاحين للاختبار:")
        print("   1. admin / admin (مدير)")
        print("   2. test / test123 (مستخدم عادي)")
        print("   3. أي مستخدم آخر في قاعدة البيانات")
        print("\n💡 نصائح الاختبار:")
        print("   - يمكنك سحب النافذة من أي مكان")
        print("   - اضغط Enter في أي حقل لتسجيل الدخول")
        print("   - جرب إدخال بيانات خاطئة لاختبار رسائل الخطأ")
        print("   - جرب ترك الحقول فارغة")
        
        # عرض شاشة الدخول
        print("\n📺 عرض شاشة الدخول...")
        result = login_screen.exec_()
        
        if result == QDialog.Accepted and authenticated_user:
            print("\n🎉 نجح الاختبار!")
            print(f"   - تم تسجيل الدخول للمستخدم: {authenticated_user.full_name}")
            print(f"   - الدور: {authenticated_user.role}")
            print("   - يمكن الآن فتح النافذة الرئيسية")
        else:
            print("\n❌ تم إلغاء تسجيل الدخول أو فشل الاختبار")
        
        print("\n🏁 انتهى الاختبار")
        
    except Exception as e:
        print(f"❌ حدث خطأ أثناء الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية للاختبار"""
    test_login_screen()

if __name__ == "__main__":
    main()
