import sys
import os
import warnings
from PyQt5.QtWidgets import QApplication, QMessageBox, QDialog
from PyQt5.QtCore import Qt, qInstallMessageHandler, QtMsgType
from PyQt5.QtGui import QFont

# إعداد إخفاء التحذيرات الشامل
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['QT_LOGGING_RULES'] = '*.debug=false;*.info=false;*.warning=false'

# إخفاء رسائل CSS غير المدعومة نهائياً
def qt_message_handler(mode, _, message):
    """مرشح رسائل Qt لإخفاء رسائل CSS غير المدعومة"""
    # قائمة الرسائل المراد إخفاؤها
    blocked_messages = [
        'Unknown property',
        'text-shadow',
        'box-shadow',
        'transform',
        'transition',
        'filter',
        'backdrop-filter',
        'overflow',
        'text-overflow',
        'cursor',
        'letter-spacing',
        'word-spacing',
        'text-decoration',
        'outline',
        'resize',
        'user-select',
        'pointer-events',
        'clip-path',
        'mask',
        'opacity',
        'visibility',
        'z-index',
        'position',
        'top',
        'left',
        'right',
        'bottom',
        'float',
        'clear',
        'display',
        'flex',
        'grid',
        'animation',
        'keyframes'
    ]

    # إخفاء الرسائل المحددة
    if any(blocked in message for blocked in blocked_messages):
        return

    # السماح بالرسائل الحرجة فقط
    if mode in [QtMsgType.QtCriticalMsg, QtMsgType.QtFatalMsg]:
        print(f"Qt Critical: {message}")

# تطبيق مرشح الرسائل
qInstallMessageHandler(qt_message_handler)

from ui.main_window import MainWindow
from database import init_db, get_session, User
from login_screen import LoginScreen

def setup_application():
    """إعداد تطبيق PyQt"""
    # تعيين خيارات الأداء العالي قبل إنشاء التطبيق
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    QApplication.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings, True)

    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)

    # تعيين اسم التطبيق
    app.setApplicationName("برنامج المحاسبة الإداري")

    # تعيين نمط التطبيق
    app.setStyle("Fusion")

    # تعيين اتجاه النص من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.RightToLeft)

    # تعيين الخط الافتراضي
    try:
        font = QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        app.setFont(font)
    except:
        pass  # تجاهل أخطاء الخط

    return app



def main():
    """الدالة الرئيسية للتطبيق مع شاشة دخول متطورة"""
    try:
        print("🚀 بدء تشغيل البرنامج...")

        # إعداد تطبيق PyQt
        app = setup_application()
        print("✅ تم إعداد التطبيق بنجاح")

        # إعداد قاعدة البيانات مع التحسينات المتقدمة
        print("🔧 إعداد قاعدة البيانات مع التحسينات المتقدمة...")
        init_db()
        print("✅ تم إعداد قاعدة البيانات مع التحسينات بنجاح")

        # إنشاء جلسة قاعدة البيانات
        session = get_session()
        print("✅ تم إنشاء جلسة قاعدة البيانات بنجاح")

        # إنشاء أو الحصول على المستخدم الإداري الافتراضي
        user = session.query(User).filter_by(role="admin").first()
        if not user:
            print("🔧 إنشاء مستخدم إداري افتراضي...")
            # إنشاء مستخدم إداري افتراضي
            user = User(
                username="admin",
                password="admin",
                role="admin",
                full_name="المدير العام"
            )
            session.add(user)
            session.commit()
            print("✅ تم إنشاء المستخدم الإداري بنجاح")

        # إنشاء وعرض شاشة الدخول
        print("🔐 إنشاء شاشة الدخول...")
        login_screen = LoginScreen()

        # متغيرات لحفظ بيانات المستخدم
        authenticated_session = None
        authenticated_user = None

        def on_login_successful(session_data, user_data):
            """معالج نجاح تسجيل الدخول"""
            nonlocal authenticated_session, authenticated_user
            authenticated_session = session_data
            authenticated_user = user_data
            print(f"✅ تم تسجيل الدخول بنجاح للمستخدم: {user_data.full_name}")

        # ربط إشارة نجاح تسجيل الدخول
        login_screen.login_successful.connect(on_login_successful)

        # عرض شاشة الدخول
        print("📺 عرض شاشة الدخول...")
        if login_screen.exec_() == QDialog.Accepted and authenticated_user:
            # تم تسجيل الدخول بنجاح، إنشاء النافذة الرئيسية
            print("🚀 إنشاء النافذة الرئيسية...")
            window = MainWindow(authenticated_session, authenticated_user)
            print("✅ تم إنشاء النافذة الرئيسية بنجاح")

            # إظهار النافذة الرئيسية فوراً
            print("📺 إظهار النافذة...")
            window.show()  # استخدام show() بدلاً من showMaximized() لتجنب المشاكل

            # تكبير النافذة بعد إظهارها بوقت كافي
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(200, window.showMaximized)

            print("🎉 تم تشغيل البرنامج بنجاح مع شاشة الدخول المتطورة!")

            # تشغيل حلقة الأحداث
            sys.exit(app.exec_())
        else:
            # تم إلغاء تسجيل الدخول
            print("❌ تم إلغاء تسجيل الدخول")
            sys.exit(0)

    except Exception as e:
        # معالجة الأخطاء غير المتوقعة
        error_message = f"❌ حدث خطأ غير متوقع: {str(e)}"
        print(error_message)

        # طباعة تفاصيل الخطأ للمطور
        import traceback
        traceback.print_exc()

        # محاولة عرض رسالة الخطأ في نافذة منبثقة
        try:
            QMessageBox.critical(None, "خطأ في البرنامج", error_message)
        except:
            # إذا فشل عرض النافذة المنبثقة، اطبع الخطأ في وحدة التحكم
            print("❌ فشل في عرض نافذة الخطأ")

        sys.exit(1)

if __name__ == "__main__":
    main()
    